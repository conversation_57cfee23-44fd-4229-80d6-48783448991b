#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压头匹配表Sheet2"对应线径"列数据识别演示脚本

这个脚本演示如何使用WireDiameterAnalyzer类来分析和识别
压头匹配.xlsx文件Sheet2中"对应线径"列的数据。

使用方法：
    python test/demo_wire_diameter_analysis.py

作者：Eplan导线处理工具
创建时间：2025-07-31
"""

import os
import sys
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test_terminal_matching_wire_diameter import WireDiameterAnalyzer


def demo_basic_analysis():
    """演示基本的数据分析功能"""
    print("=" * 60)
    print("演示1：基本数据分析")
    print("=" * 60)
    
    file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
    analyzer = WireDiameterAnalyzer(file_path)
    
    # 加载数据
    if not analyzer.load_data():
        print("数据加载失败，演示终止")
        return
    
    # 显示原始数据
    print("\n1. 原始数据预览：")
    print(analyzer.wire_diameter_data.head(10))
    
    # 数据类型分析
    print("\n2. 数据类型分析：")
    analysis = analyzer.analyze_data_types()
    for key, value in analysis.items():
        if key != "值计数":  # 值计数单独显示
            print(f"   {key}: {value}")
    
    print("\n3. 详细值计数：")
    for value, count in analysis["值计数"].items():
        print(f"   '{value}': {count}条记录")


def demo_data_validation():
    """演示数据验证功能"""
    print("\n" + "=" * 60)
    print("演示2：数据验证")
    print("=" * 60)
    
    file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
    analyzer = WireDiameterAnalyzer(file_path)
    
    if not analyzer.load_data():
        return
    
    validation = analyzer.validate_data()
    
    print(f"\n数据验证结果：")
    print(f"   有效记录数: {validation['有效记录数']}")
    print(f"   无效记录数: {validation['无效记录数']}")
    print(f"   数值范围: {validation['数值范围']['最小值']} ~ {validation['数值范围']['最大值']}")
    
    if validation['异常值']:
        print(f"\n异常值列表：")
        for anomaly in validation['异常值']:
            print(f"   {anomaly}")
    else:
        print("\n✓ 未发现异常值")
    
    if validation['建议']:
        print(f"\n建议：")
        for suggestion in validation['建议']:
            print(f"   • {suggestion}")


def demo_data_formatting():
    """演示数据格式化功能"""
    print("\n" + "=" * 60)
    print("演示3：数据格式化")
    print("=" * 60)
    
    file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
    analyzer = WireDiameterAnalyzer(file_path)
    
    if not analyzer.load_data():
        return
    
    # 原始数据
    print("\n原始数据（前10条）：")
    original_data = analyzer.wire_diameter_data.head(10)
    for i, value in enumerate(original_data):
        print(f"   行{i+1}: {value} (类型: {type(value).__name__})")
    
    # 格式化后的数据
    print("\n格式化后的数据（前10条）：")
    formatted_data = analyzer.format_data().head(10)
    for i, value in enumerate(formatted_data):
        print(f"   行{i+1}: {value} (类型: {type(value).__name__})")


def demo_specific_data_queries():
    """演示特定数据查询功能"""
    print("\n" + "=" * 60)
    print("演示4：特定数据查询")
    print("=" * 60)
    
    file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
    analyzer = WireDiameterAnalyzer(file_path)
    
    if not analyzer.load_data():
        return
    
    # 查询特定线径的压头
    print("\n1. 查询线径为1.5的压头：")
    diameter_15_rows = analyzer.sheet2_data[analyzer.sheet2_data['对应线径'] == 1.5]
    for idx, row in diameter_15_rows.iterrows():
        print(f"   {row['压头名称']} - {row['接口类型']} - {row['并线要求']}")
    
    print(f"\n2. 查询特殊值（非数值）的压头：")
    special_values = ['屏蔽层', '地线/屏蔽层', '/']
    for special_val in special_values:
        special_rows = analyzer.sheet2_data[analyzer.sheet2_data['对应线径'] == special_val]
        if not special_rows.empty:
            print(f"\n   对应线径为'{special_val}'的压头：")
            for idx, row in special_rows.iterrows():
                print(f"     {row['压头名称']} - {row['接口类型']}")
    
    print(f"\n3. 线径数值分布统计：")
    numeric_diameters = []
    for value in analyzer.wire_diameter_data:
        try:
            if isinstance(value, (int, float)) and value > 0:
                numeric_diameters.append(value)
            elif isinstance(value, str) and value not in special_values:
                numeric_diameters.append(float(value))
        except:
            pass
    
    if numeric_diameters:
        numeric_series = pd.Series(numeric_diameters)
        print(f"     最小值: {numeric_series.min()}")
        print(f"     最大值: {numeric_series.max()}")
        print(f"     平均值: {numeric_series.mean():.2f}")
        print(f"     中位数: {numeric_series.median()}")


def demo_export_analysis():
    """演示分析结果导出功能"""
    print("\n" + "=" * 60)
    print("演示5：分析结果导出")
    print("=" * 60)
    
    file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
    analyzer = WireDiameterAnalyzer(file_path)
    
    if not analyzer.load_data():
        return
    
    # 生成详细报告
    report = analyzer.generate_report()
    
    # 保存报告到文件
    output_dir = "test/output"
    os.makedirs(output_dir, exist_ok=True)
    
    report_file = os.path.join(output_dir, "wire_diameter_analysis_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n✓ 分析报告已保存到: {report_file}")
    
    # 导出详细数据到Excel
    excel_file = os.path.join(output_dir, "wire_diameter_detailed_analysis.xlsx")
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # 原始数据
        analyzer.sheet2_data.to_excel(writer, sheet_name='原始数据', index=False)
        
        # 对应线径列分析
        diameter_analysis = pd.DataFrame({
            '行号': range(1, len(analyzer.wire_diameter_data) + 1),
            '原始值': analyzer.wire_diameter_data,
            '格式化值': analyzer.format_data(),
            '数据类型': [type(x).__name__ for x in analyzer.wire_diameter_data]
        })
        diameter_analysis.to_excel(writer, sheet_name='对应线径分析', index=False)
        
        # 统计汇总
        analysis = analyzer.analyze_data_types()
        summary_data = []
        for key, value in analysis.items():
            if key != "值计数":
                summary_data.append({'项目': key, '值': str(value)})
        
        # 值计数统计
        for value, count in analysis["值计数"].items():
            summary_data.append({'项目': f'值计数_{value}', '值': count})
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
    
    print(f"✓ 详细分析数据已导出到: {excel_file}")


def main():
    """主函数"""
    print("压头匹配表Sheet2\"对应线径\"列数据识别演示")
    print("=" * 60)
    
    try:
        # 运行各个演示
        demo_basic_analysis()
        demo_data_validation()
        demo_data_formatting()
        demo_specific_data_queries()
        demo_export_analysis()
        
        print("\n" + "=" * 60)
        print("✓ 所有演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
