import time
import logging
import pandas as pd
from playwright.sync_api import sync_playwright
import re
import os
from datetime import datetime

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,  # 更详细的调试级别
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("design_task_debug.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)

def extract_engineering_tasks(url):
    """提取设计任务合并表数据 - 完全匹配截图布局"""
    results = {"tasks": [], "status": "error", "message": "", "debug_info": {}}
    
    try:
        with sync_playwright() as p:
            # 启动浏览器（可见模式便于调试）
            browser = p.chromium.launch(headless=False)
            context = browser.new_context(
                viewport={"width": 1600, "height": 900},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                device_scale_factor=1.5  # 确保清晰显示
            )
            page = context.new_page()
            
            # 访问目标页面
            logging.info(f"正在访问设计任务系统: {url}")
            start_time = datetime.now()
            navigation = page.goto(url, timeout=300000, wait_until="networkidle")  # 延长超时到5分钟
            
            # 检查HTTP状态
            if navigation.status != 200:
                results["message"] = f"页面加载失败，HTTP状态码: {navigation.status}"
                browser.close()
                return results
            
            # 等待查询区域加载 - 根据截图中的搜索框特征
            logging.debug("等待查询条件区域加载...")
            try:
                # 使用更通用的选择器匹配查询区域
                search_area = page.wait_for_selector('div.search-area, div.query-panel, div.filter-container', timeout=60000)
                if not search_area:
                    raise Exception("查询区域加载失败")
                logging.info("查询区域加载成功")
            except Exception as e:
                # 保存截图用于分析
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = f"query_error_{timestamp}.png"
                page.screenshot(path=screenshot_path, full_page=True)
                
                # 保存页面源码
                html_path = f"page_source_{timestamp}.html"
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(page.content())
                
                results["message"] = f"查询区域加载失败: {str(e)} | 截图已保存: {screenshot_path}"
                results["debug_info"] = {"screenshot": screenshot_path, "html": html_path}
                browser.close()
                return results
            
            # 定位核心功能按钮（基于截图描述）
            buttons_mapping = {}
            button_selectors = {
                "query_btn": ':text("查询"), :text("搜索"), :text("查找")',
                "advanced_query": ':text("高级查询")',
                "export_btn": ':text("导出")'
            }
            
            logging.info("识别页面功能按钮...")
            for btn_name, selector in button_selectors.items():
                elements = page.locator(selector)
                if elements.count() > 0:
                    buttons_mapping[btn_name] = elements.first
                    logging.debug(f"找到按钮: {btn_name} - {elements.first.inner_text()}")
            
            # 验证至少找到查询按钮
            if "query_btn" not in buttons_mapping:
                results["message"] = "未找到核心查询按钮，界面布局不匹配"
                browser.close()
                return results
            
            # 点击查询按钮初始化数据
            try:
                logging.info("点击查询按钮加载数据...")
                with page.expect_response(lambda response: response.url == url and response.status == 200):
                    buttons_mapping["query_btn"].click()
                page.wait_for_load_state("networkidle", timeout=60000)
                logging.info("数据加载完成")
            except Exception as e:
                results["message"] = f"查询执行失败: {str(e)}"
                browser.close()
                return results
            
            # 定位任务表格 - 基于截图中展示的表格特征
            logging.info("定位任务表格...")
            table = None
            table_selectors = [
                "table.datagrid",
                "table.grid-view",
                "table.data-table",
                "table:has(thead tr th >> text=流水号)",
                "table:has(tbody tr td >> text=待执行)"
            ]
            
            for selector in table_selectors:
                if page.locator(selector).count() > 0:
                    table = page.locator(selector).first
                    logging.info(f"使用选择器找到表格: {selector}")
                    break
            
            if not table:
                # 尝试通过表格临近元素定位
                logging.warning("直接定位表格失败，尝试通过附近元素定位")
                if "export_btn" in buttons_mapping:
                    table = buttons_mapping["export_btn"].locator("xpath=preceding::table[1]")
                elif page.locator('.pagination').count() > 0:
                    table = page.locator('.pagination').locator("xpath=preceding::table[1]")
                
                if table and table.count() > 0:
                    logging.info("通过导出按钮或分页器定位到表格")
            
            if not table:
                # 保存当前状态用于分析
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = f"table_error_{timestamp}.png"
                page.screenshot(path=screenshot_path, full_page=True)
                html_path = f"table_source_{timestamp}.html"
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(page.content())
                
                results["message"] = "无法定位任务表格，请参考截图分析页面结构"
                results["debug_info"] = {"screenshot": screenshot_path, "html": html_path}
                browser.close()
                return results
            
            # 提取表格列头信息
            logging.info("解析表格列头...")
            columns = []
            col_elements = table.locator("thead tr th, thead tr td").all()
            if not col_elements:
                col_elements = table.locator("tbody tr:first-child td").all()
            
            for col in col_elements:
                col_text = col.inner_text().strip()
                if col_text:
                    columns.append(col_text)
            logging.info(f"识别到列头: {columns}")
            
            # 提取表格数据（处理分页）
            all_rows = []
            current_page = 1
            max_pages = 2  # 仅提取2页作为演示
            
            while current_page <= max_pages:
                logging.info(f"提取第 {current_page} 页数据...")
                
                # 等待表格内容加载
                table.locator("tbody tr").first.wait_for(timeout=30000)
                
                # 获取所有行
                rows = table.locator("tbody tr").all()
                logging.debug(f"本页找到 {len(rows)} 行数据")
                
                for row in rows:
                    row_data = {}
                    cells = row.locator("td").all()
                    
                    for col_idx, cell in enumerate(cells):
                        col_name = columns[col_idx] if col_idx < len(columns) else f"列{col_idx+1}"
                        
                        # 特殊处理重点工程列（红叉）
                        if "重点" in col_name:
                            has_img = cell.locator("img").count() > 0
                            alt_text = ""
                            if has_img:
                                img_element = cell.locator("img").first
                                alt_text = img_element.get_attribute("alt") or ""
                            row_data[col_name] = "是" if has_img and ("叉" in alt_text or "×" in alt_text) else "否"
                        else:
                            # 提取单元格文本内容
                            row_data[col_name] = cell.inner_text().replace("\n", " ").strip()
                    
                    # 添加分页信息
                    row_data["当前页码"] = current_page
                    
                    # 处理空行
                    if any(row_data.values()):
                        all_rows.append(row_data)
                
                # 检查分页并尝试翻页
                current_page += 1
                if current_page > max_pages:
                    break
                
                next_btn = page.locator(':text("下一页"), :text(">")')
                if next_btn.count() > 0 and next_btn.first.is_enabled():
                    with page.expect_navigation(timeout=60000):
                        next_btn.first.click()
                    
                    # 等待新表格加载
                    page.wait_for_load_state("networkidle")
                    time.sleep(3)  # 额外等待
                    
                    # 重新定位表格
                    table = table.first
                else:
                    logging.info("已到达最后一页或分页按钮不可用")
                    break
            
            # 整理结果
            results["tasks"] = all_rows
            results["status"] = "success"
            results["message"] = f"成功提取 {len(all_rows)} 条任务记录（来自 {current_page-1} 页）"
            
            # 添加性能统计
            duration = (datetime.now() - start_time).total_seconds()
            results["debug_info"]["duration"] = f"{duration:.2f}秒"
            
            browser.close()
            return results
    
    except Exception as e:
        logging.exception("处理过程中发生错误")
        results["message"] = f"运行时错误: {str(e)}"
        return results

def save_to_excel(tasks, filename="设计任务合并表.xlsx"):
    """保存数据到Excel"""
    if not tasks:
        return None
    
    try:
        df = pd.DataFrame(tasks)
        
        # 按常见字段排序
        column_order = ["流水号", "设计合并时间", "工程编号", "工程名称", 
                       "工程专业", "是否重点工程", "任务状态", "当前设计人"]
        
        # 只保留存在的列
        existing_cols = [col for col in column_order if col in df.columns]
        other_cols = [col for col in df.columns if col not in existing_cols]
        
        df = df[existing_cols + other_cols]
        df.to_excel(filename, index=False, engine="openpyxl")
        
        logging.info(f"已保存Excel文件: {os.path.abspath(filename)}")
        return os.path.abspath(filename)
    except Exception as e:
        logging.error(f"保存Excel失败: {str(e)}")
        return None

if __name__ == "__main__":
    # 目标URL
    task_url = "http://publishserver03.sznari.com:8050/designwork/designwork_mergetasklist.aspx?TableConfigId=f470c114-ff34-43f0-8f70-d02c75d81732&p1=%e7%94%9f%e4%ba%a7%e4%bb%bb%e5%8a%a1%e6%89%a7%e8%a1%8c&p2=%e6%89%80%e6%9c%89%e4%bb%bb%e5%8a%a1&uid=luoxl&moduleid=107&modulename=%e6%89%80%e6%9c%89%e4%bb%bb%e5%8a%a1"
    
    # 执行提取
    result = extract_engineering_tasks(task_url)
    
    # 处理结果
    print("\n=== 设计任务提取报告 ===")
    print(f"状态: {result['status'].upper()}")
    print(f"结果: {result['message']}")
    
    if result.get("debug_info"):
        print(f"\n调试信息: {result['debug_info']}")
    
    if result["status"] == "success" and result["tasks"]:
        # 显示前2条记录
        print("\n数据样本（前2条）：")
        for i, task in enumerate(result["tasks"][:2]):
            print(f"\n记录 #{i+1}:")
            for key, value in task.items():
                if not key.startswith("_"):  # 跳过调试字段
                    print(f"  {key}: {value}")
        
        # 保存完整数据
        excel_file = save_to_excel(result["tasks"])
        if excel_file:
            print(f"\n完整数据已保存至: {excel_file}")
    
    print("\n=== 报告结束 ===")