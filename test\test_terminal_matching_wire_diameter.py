#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压头匹配表Sheet2中"对应线径"列数据识别测试代码

功能：
1. 读取压头匹配.xlsx文件Sheet2的"对应线径"列数据
2. 识别和分类不同类型的数据
3. 进行数据验证和统计分析
4. 提供数据清洗和格式化功能

作者：Eplan导线处理工具
创建时间：2025-07-31
"""

import pandas as pd
import numpy as np
import os
import sys
import unittest
from typing import Dict, List, Tuple, Any, Optional

# 添加项目根目录到路径，以便导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from terminal_matching import format_diameter
except ImportError:
    # 如果无法导入，提供一个简单的格式化函数
    def format_diameter(diameter):
        """简单的线径格式化函数"""
        try:
            if pd.isna(diameter):
                return None
            if isinstance(diameter, str):
                if diameter.strip() in ['/', '屏蔽层', '地线/屏蔽层']:
                    return diameter.strip()
                try:
                    return float(diameter)
                except ValueError:
                    return diameter.strip()
            return float(diameter)
        except:
            return diameter


class WireDiameterAnalyzer:
    """对应线径列数据分析器"""
    
    def __init__(self, file_path: str):
        """
        初始化分析器
        
        Args:
            file_path: 压头匹配.xlsx文件路径
        """
        self.file_path = file_path
        self.sheet2_data = None
        self.wire_diameter_data = None
        
    def load_data(self) -> bool:
        """
        加载Excel文件数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.file_path):
                print(f"错误：文件不存在 - {self.file_path}")
                return False
                
            # 读取Sheet2数据
            self.sheet2_data = pd.read_excel(self.file_path, sheet_name=1)
            print(f"成功加载Sheet2数据，形状：{self.sheet2_data.shape}")
            
            # 检查是否存在"对应线径"列
            if '对应线径' not in self.sheet2_data.columns:
                print("错误：Sheet2中未找到'对应线径'列")
                print(f"可用列名：{list(self.sheet2_data.columns)}")
                return False
                
            # 提取对应线径列数据
            self.wire_diameter_data = self.sheet2_data['对应线径'].copy()
            print(f"成功提取'对应线径'列数据，共{len(self.wire_diameter_data)}条记录")
            
            return True
            
        except Exception as e:
            print(f"加载数据时发生错误：{e}")
            return False
    
    def analyze_data_types(self) -> Dict[str, Any]:
        """
        分析对应线径列的数据类型
        
        Returns:
            Dict: 数据类型分析结果
        """
        if self.wire_diameter_data is None:
            return {"error": "数据未加载"}
            
        analysis = {
            "总记录数": len(self.wire_diameter_data),
            "数据类型": str(self.wire_diameter_data.dtype),
            "唯一值数量": self.wire_diameter_data.nunique(),
            "空值数量": self.wire_diameter_data.isna().sum(),
            "唯一值列表": list(self.wire_diameter_data.unique()),
            "值计数": self.wire_diameter_data.value_counts().to_dict()
        }
        
        # 分类数据类型
        numeric_values = []
        string_values = []
        special_values = []
        
        for value in self.wire_diameter_data.dropna().unique():
            if isinstance(value, (int, float)):
                numeric_values.append(value)
            elif isinstance(value, str):
                if value in ['/', '屏蔽层', '地线/屏蔽层']:
                    special_values.append(value)
                else:
                    try:
                        float(value)
                        numeric_values.append(float(value))
                    except ValueError:
                        string_values.append(value)
            else:
                special_values.append(value)
        
        analysis["数值型数据"] = sorted(numeric_values)
        analysis["字符串型数据"] = string_values
        analysis["特殊值"] = special_values
        
        return analysis
    
    def validate_data(self) -> Dict[str, Any]:
        """
        验证数据的有效性
        
        Returns:
            Dict: 数据验证结果
        """
        if self.wire_diameter_data is None:
            return {"error": "数据未加载"}
            
        validation = {
            "有效记录数": 0,
            "无效记录数": 0,
            "数值范围": {"最小值": None, "最大值": None},
            "异常值": [],
            "建议": []
        }
        
        valid_count = 0
        invalid_count = 0
        numeric_values = []
        
        for idx, value in enumerate(self.wire_diameter_data):
            if pd.isna(value):
                invalid_count += 1
                validation["异常值"].append(f"行{idx+1}: 空值")
                continue
                
            # 检查是否为有效的线径值
            if isinstance(value, (int, float)):
                if value > 0:
                    valid_count += 1
                    numeric_values.append(value)
                else:
                    invalid_count += 1
                    validation["异常值"].append(f"行{idx+1}: 数值无效 ({value})")
            elif isinstance(value, str):
                if value.strip() in ['/', '屏蔽层', '地线/屏蔽层']:
                    valid_count += 1  # 这些是特殊但有效的值
                else:
                    try:
                        num_val = float(value.strip())
                        if num_val > 0:
                            valid_count += 1
                            numeric_values.append(num_val)
                        else:
                            invalid_count += 1
                            validation["异常值"].append(f"行{idx+1}: 数值无效 ({value})")
                    except ValueError:
                        invalid_count += 1
                        validation["异常值"].append(f"行{idx+1}: 无法解析 ({value})")
            else:
                invalid_count += 1
                validation["异常值"].append(f"行{idx+1}: 未知类型 ({value})")
        
        validation["有效记录数"] = valid_count
        validation["无效记录数"] = invalid_count
        
        if numeric_values:
            validation["数值范围"]["最小值"] = min(numeric_values)
            validation["数值范围"]["最大值"] = max(numeric_values)
        
        # 生成建议
        if invalid_count > 0:
            validation["建议"].append(f"发现{invalid_count}条无效记录，建议检查数据质量")
        if len(validation["异常值"]) > 10:
            validation["建议"].append("异常值较多，建议进行数据清洗")
            
        return validation
    
    def format_data(self) -> pd.Series:
        """
        格式化对应线径数据
        
        Returns:
            pd.Series: 格式化后的数据
        """
        if self.wire_diameter_data is None:
            return pd.Series()
            
        formatted_data = self.wire_diameter_data.apply(format_diameter)
        return formatted_data
    
    def generate_report(self) -> str:
        """
        生成分析报告
        
        Returns:
            str: 分析报告文本
        """
        if not self.load_data():
            return "无法生成报告：数据加载失败"
            
        analysis = self.analyze_data_types()
        validation = self.validate_data()
        
        report = f"""
=== 压头匹配表Sheet2"对应线径"列数据分析报告 ===

文件路径：{self.file_path}

1. 基本信息：
   - 总记录数：{analysis['总记录数']}
   - 数据类型：{analysis['数据类型']}
   - 唯一值数量：{analysis['唯一值数量']}
   - 空值数量：{analysis['空值数量']}

2. 数据分布：
   - 数值型数据：{analysis['数值型数据']}
   - 字符串型数据：{analysis['字符串型数据']}
   - 特殊值：{analysis['特殊值']}

3. 值计数统计：
"""
        for value, count in analysis['值计数'].items():
            report += f"   - {value}: {count}条\n"
        
        report += f"""
4. 数据验证：
   - 有效记录数：{validation['有效记录数']}
   - 无效记录数：{validation['无效记录数']}
   - 数值范围：{validation['数值范围']['最小值']} ~ {validation['数值范围']['最大值']}

5. 异常值（前10条）：
"""
        for i, anomaly in enumerate(validation['异常值'][:10]):
            report += f"   - {anomaly}\n"
            
        if len(validation['异常值']) > 10:
            report += f"   ... 还有{len(validation['异常值']) - 10}条异常值\n"
        
        report += f"""
6. 建议：
"""
        for suggestion in validation['建议']:
            report += f"   - {suggestion}\n"
            
        return report


class TestWireDiameterAnalyzer(unittest.TestCase):
    """对应线径分析器的单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
        self.analyzer = WireDiameterAnalyzer(self.file_path)
    
    def test_file_exists(self):
        """测试文件是否存在"""
        self.assertTrue(os.path.exists(self.file_path), f"文件不存在：{self.file_path}")
    
    def test_load_data(self):
        """测试数据加载"""
        result = self.analyzer.load_data()
        self.assertTrue(result, "数据加载失败")
        self.assertIsNotNone(self.analyzer.sheet2_data, "Sheet2数据为空")
        self.assertIsNotNone(self.analyzer.wire_diameter_data, "对应线径数据为空")
    
    def test_analyze_data_types(self):
        """测试数据类型分析"""
        self.analyzer.load_data()
        analysis = self.analyzer.analyze_data_types()
        
        self.assertIn("总记录数", analysis)
        self.assertIn("唯一值列表", analysis)
        self.assertIn("数值型数据", analysis)
        self.assertGreater(analysis["总记录数"], 0)
    
    def test_validate_data(self):
        """测试数据验证"""
        self.analyzer.load_data()
        validation = self.analyzer.validate_data()
        
        self.assertIn("有效记录数", validation)
        self.assertIn("无效记录数", validation)
        self.assertIsInstance(validation["异常值"], list)


def main():
    """主函数"""
    print("=== 压头匹配表Sheet2对应线径列数据识别测试 ===\n")
    
    # 文件路径
    file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
    
    # 创建分析器
    analyzer = WireDiameterAnalyzer(file_path)
    
    # 生成并打印报告
    report = analyzer.generate_report()
    print(report)
    
    # 运行单元测试
    print("\n=== 运行单元测试 ===")
    unittest.main(argv=[''], exit=False, verbosity=2)


if __name__ == "__main__":
    main()
