#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试format_diameter函数修复后的效果

验证"地线/屏蔽层"字符串是否能正确识别，不再被错误地格式化为"屏蔽层"

作者：Eplan导线处理工具
创建时间：2025-07-31
"""

import os
import sys
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from terminal_matching import format_diameter


def test_format_diameter_function():
    """测试format_diameter函数对各种输入的处理"""
    print("=" * 60)
    print("测试format_diameter函数")
    print("=" * 60)
    
    test_cases = [
        # 数值型测试
        (0.12, "0.1"),
        (0.3, "0.3"),
        (1, "1.0"),
        (1.5, "1.5"),
        (2.5, "2.5"),
        (4, "4.0"),
        (10, "10.0"),
        
        # 字符串型数值测试
        ("0.12", "0.1"),
        ("1.5", "1.5"),
        ("2.5", "2.5"),
        
        # 特殊值测试
        ("屏蔽层", "屏蔽层"),
        ("地线/屏蔽层", "地线/屏蔽层"),  # 这是关键测试用例
        ("/", "/"),
        
        # 边界情况测试
        (None, ""),
        ("", ""),
        ("  屏蔽层  ", "屏蔽层"),
        ("  地线/屏蔽层  ", "地线/屏蔽层"),
    ]
    
    print("测试用例结果：")
    print("-" * 60)
    
    all_passed = True
    for i, (input_val, expected) in enumerate(test_cases, 1):
        try:
            result = format_diameter(input_val)
            status = "✓ PASS" if result == expected else "✗ FAIL"
            if result != expected:
                all_passed = False
            
            print(f"{i:2d}. 输入: {repr(input_val):15} → 输出: {repr(result):15} 期望: {repr(expected):15} {status}")
            
            if result != expected:
                print(f"    ❌ 错误：期望 {repr(expected)}，但得到 {repr(result)}")
                
        except Exception as e:
            print(f"{i:2d}. 输入: {repr(input_val):15} → 错误: {e}")
            all_passed = False
    
    print("-" * 60)
    if all_passed:
        print("✅ 所有测试用例通过！")
    else:
        print("❌ 部分测试用例失败！")
    
    return all_passed


def test_excel_data_processing():
    """测试Excel数据处理"""
    print("\n" + "=" * 60)
    print("测试Excel数据处理")
    print("=" * 60)
    
    file_path = os.path.join("input", "配置文件", "压头匹配.xlsx")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取原始数据
        sheet2 = pd.read_excel(file_path, sheet_name=1)
        original_data = sheet2['对应线径'].copy()
        
        print("原始数据中的唯一值：")
        for value in sorted(original_data.unique(), key=str):
            count = (original_data == value).sum()
            print(f"  {repr(value):15} : {count}条记录")
        
        # 应用格式化
        formatted_data = original_data.apply(format_diameter)
        
        print("\n格式化后的唯一值：")
        for value in sorted(formatted_data.unique(), key=str):
            count = (formatted_data == value).sum()
            print(f"  {repr(value):15} : {count}条记录")
        
        # 检查关键问题：地线/屏蔽层是否被正确保留
        original_has_ground_shield = "地线/屏蔽层" in original_data.values
        formatted_has_ground_shield = "地线/屏蔽层" in formatted_data.values
        
        print(f"\n关键检查：")
        print(f"  原始数据包含'地线/屏蔽层': {original_has_ground_shield}")
        print(f"  格式化后包含'地线/屏蔽层': {formatted_has_ground_shield}")
        
        if original_has_ground_shield and formatted_has_ground_shield:
            print("  ✅ '地线/屏蔽层'被正确保留")
            return True
        elif original_has_ground_shield and not formatted_has_ground_shield:
            print("  ❌ '地线/屏蔽层'被错误地格式化了")
            return False
        else:
            print("  ℹ️  原始数据中没有'地线/屏蔽层'")
            return True
            
    except Exception as e:
        print(f"❌ 处理Excel数据时出错: {e}")
        return False


def test_specific_cases():
    """测试特定的问题案例"""
    print("\n" + "=" * 60)
    print("测试特定问题案例")
    print("=" * 60)
    
    # 这些是从实际数据中发现的问题案例
    problem_cases = [
        "地线/屏蔽层",  # 这个应该保持不变
        "屏蔽层",       # 这个应该保持不变
        "地线",         # 这个应该变成"屏蔽层"
    ]
    
    print("问题案例测试：")
    for case in problem_cases:
        result = format_diameter(case)
        print(f"  输入: {repr(case):15} → 输出: {repr(result)}")
        
        # 特别检查"地线/屏蔽层"是否被正确处理
        if case == "地线/屏蔽层":
            if result == "地线/屏蔽层":
                print(f"    ✅ 正确：'地线/屏蔽层'被保留")
            else:
                print(f"    ❌ 错误：'地线/屏蔽层'被改为'{result}'")
                return False
    
    return True


def main():
    """主函数"""
    print("format_diameter函数修复验证测试")
    print("=" * 60)
    
    # 运行所有测试
    test1_passed = test_format_diameter_function()
    test2_passed = test_excel_data_processing()
    test3_passed = test_specific_cases()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if test1_passed and test2_passed and test3_passed:
        print("✅ 所有测试通过！format_diameter函数修复成功。")
        print("   '地线/屏蔽层'现在能够正确识别，不会被错误地格式化为'屏蔽层'。")
    else:
        print("❌ 部分测试失败！需要进一步检查和修复。")
        
        if not test1_passed:
            print("   - format_diameter函数测试失败")
        if not test2_passed:
            print("   - Excel数据处理测试失败")
        if not test3_passed:
            print("   - 特定问题案例测试失败")


if __name__ == "__main__":
    main()
